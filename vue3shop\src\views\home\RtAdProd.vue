<template>
  <div class="APContainer">
    <div class="APBlock">
      <span class="APContainer-title">国家补贴X百亿补贴</span>
      <div class="APProd">
        <img :src="GJBT.src" alt="国家补贴" class="APImg" />
        <div class="APInfo">
          <div class="APSpan">已补{{GJBT.Albutie}}元</div>
          <div class="APPrice">￥{{ GJBT.price }}</div>
        </div>
      </div>
    </div>
    <div class="APBlock">
      <span class="APContainer-title">国家补贴X百亿补贴</span>
      <div class="APProd">
        <img :src="GJBT2.src" alt="国家补贴" class="APImg" />
        <div class="APInfo">
          <div class="APSpan">已补{{GJBT2.Albutie}}元</div>
          <div class="APPrice">￥{{ GJBT2.price }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import guobu from '@/assets/images/my/国家补贴.jpg'
import guobu2 from '@/assets/images/my/国家补贴2.jpg'
const GJBT = reactive({
  src: guobu,
  Albutie: 1780,
  price: 4890
})
const GJBT2 = reactive({
  src: guobu2,
  Albutie: 1000,
  price: 8900
})
</script>

<style scoped>
.APContainer {
  display: flex;
  flex-direction: row;
  background: #fffbe7;
  border-radius: 18px;
  box-shadow: 0 2px 12px #ffe082;
  padding: 28px 38px 28px 38px;
  max-width: 900px;
  min-width: 320px;
  margin: 0 auto;
  align-items: flex-start;
  gap: 48px;
}
.APBlock {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.APContainer-title {
  font-size: 22px;
  font-weight: bold;
  color: #b8860b;
  margin-bottom: 18px;
  letter-spacing: 1px;
  text-align: center;
}
.APProd {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
}
.APImg {
  width: 200px;
  height: 200px;
  border-radius: 18px;
  object-fit: cover;
  box-shadow: 0 1px 12px #ffe082;
  background: #fff;
}
.APInfo {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}
.APSpan {
  font-size: 18px;
  color: #e2231a;
  font-weight: 600;
}
.APPrice {
  font-size: 28px;
  color: #d32f2f;
  font-weight: bold;
  margin-top: 4px;
}
@media (max-width: 900px) {
  .APContainer {
    flex-direction: column;
    gap: 24px;
    padding: 12px 6px;
    border-radius: 10px;
    box-shadow: none;
    max-width: 98vw;
  }
  .APBlock {
    margin-bottom: 12px;
  }
  .APImg {
    width: 100px;
    height: 100px;
  }
  .APContainer-title {
    font-size: 15px;
    margin-bottom: 8px;
  }
  .APSpan, .APPrice {
    font-size: 13px;
  }
}
</style>