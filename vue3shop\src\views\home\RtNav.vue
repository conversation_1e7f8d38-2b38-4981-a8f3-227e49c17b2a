<template>
  <el-card class="nav-container" shadow="hover">
    <div class="nav-list">
      <el-tooltip
        v-for="item in dataList"
        :key="item.id"
        :content="item.name"
        placement="right"
        effect="light"
      >
        <div class="nav-item" @click="handleNavClick(item)">
          <el-icon class="nav-icon">
            <Monitor v-if="item.id === 1" />
            <ShoppingCart v-else-if="item.id === 2" />
            <Connection v-else-if="item.id === 3" />
            <Mouse v-else-if="item.id === 4" />
          </el-icon>
          <span class="nav-name">{{ item.name }}</span>
        </div>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup>
import { Monitor, ShoppingCart, Connection, Mouse } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { productCategoryListService } from '@/api/product'
import { useUserStore } from '@/stores/user'
import dingzhi from '@/assets/images/navs/定制.svg'
import xuangou from '@/assets/images/navs/电脑机箱-01.svg'
import peijian from '@/assets/images/navs/数码配件.svg'
import waishe from '@/assets/images/navs/外设.svg'

const router = useRouter()
const userStore = useUserStore()

const dataList = [
  { id: 1, name: '定制diy电脑', src: dingzhi, path: '/diy-computer' },
  { id: 2, name: '选购现成diy电脑', src: xuangou, action: 'diy-category' },
  { id: 3, name: '数码配件', src: peijian, path: '/category' },
  { id: 4, name: '外设推荐', src: waishe, path: '/category' }
]

// 跳转到DIY电脑分类页面
const goToDiyComputerCategory = async () => {
  if (!userStore.checkLogin()) return

  try {
    console.log('开始查找DIY电脑分类...')
    const result = await productCategoryListService()

    if (result && result.data) {
      // 查找"DIY电脑"分类
      const diyCategory = result.data.find(cat =>
        cat.categoryName === 'DIY电脑' ||
        cat.categoryName === 'diy电脑' ||
        cat.categoryName === 'DIY' ||
        cat.categoryName.toLowerCase().includes('diy')
      )

      if (diyCategory) {
        console.log('找到DIY电脑分类:', diyCategory)
        // 跳转到分类详情页
        router.push({ name: 'CategoryDetail', params: { id: diyCategory.categoryId } })
      } else {
        console.log('未找到DIY电脑分类')
        console.log('可用的分类:', result.data.map(cat => cat.categoryName))
        ElMessage.warning('未找到DIY电脑分类，跳转到分类列表页')
        router.push('/category')
      }
    } else {
      console.error('获取分类列表失败')
      ElMessage.error('获取分类列表失败，跳转到分类列表页')
      router.push('/category')
    }
  } catch (error) {
    console.error('跳转失败:', error)
    ElMessage.error('跳转失败，跳转到分类列表页')
    router.push('/category')
  }
}

const handleNavClick = (item) => {
  // 处理导航点击事件
  console.log('点击了:', item.name)

  if (item.action === 'diy-category') {
    // 特殊处理：跳转到DIY电脑分类
    goToDiyComputerCategory()
  } else if (item.path) {
    router.push(item.path)
  }
}
</script>

<style scoped>
.nav-container {
  width: 120px;
  background: #fff;
  border-radius: 16px;
  margin-left: 18px;
  padding: 16px 0;
  transition: all 0.3s ease;
}

.nav-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: #f5f7fa;
  transform: scale(1.05);
}

.nav-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
  padding: 8px;
  background: #ecf5ff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-icon {
  background: #409EFF;
  color: #fff;
  transform: rotate(10deg);
}

.nav-name {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

.nav-item:hover .nav-name {
  color: #409EFF;
}

:deep(.el-card__body) {
  padding: 0;
}
</style>