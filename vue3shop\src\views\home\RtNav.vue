<template>
  <el-card class="nav-container" shadow="hover">
    <div class="nav-list">
      <el-tooltip
        v-for="item in dataList"
        :key="item.id"
        :content="item.name"
        placement="right"
        effect="light"
      >
        <div class="nav-item" @click="handleNavClick(item)">
          <el-icon class="nav-icon">
            <Monitor v-if="item.id === 1" />
            <ShoppingCart v-else-if="item.id === 2" />
            <Connection v-else-if="item.id === 3" />
            <Mouse v-else-if="item.id === 4" />
          </el-icon>
          <span class="nav-name">{{ item.name }}</span>
        </div>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup>
import { Monitor, ShoppingCart, Connection, Mouse } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import dingzhi from '@/assets/images/navs/定制.svg'
import xuangou from '@/assets/images/navs/电脑机箱-01.svg'
import peijian from '@/assets/images/navs/数码配件.svg'
import waishe from '@/assets/images/navs/外设.svg'

const router = useRouter()

const dataList = [
  { id: 1, name: '定制diy电脑', src: dingzhi, path: '/diy-computer' },
  { id: 2, name: '选购现成diy电脑', src: xuangou, path: '/category' },
  { id: 3, name: '数码配件', src: peijian, path: '/category' },
  { id: 4, name: '外设推荐', src: waishe, path: '/category' }
]

const handleNavClick = (item) => {
  // 处理导航点击事件
  console.log('点击了:', item.name)
  if (item.path) {
    router.push(item.path)
  }
}
</script>

<style scoped>
.nav-container {
  width: 120px;
  background: #fff;
  border-radius: 16px;
  margin-left: 18px;
  padding: 16px 0;
  transition: all 0.3s ease;
}

.nav-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: #f5f7fa;
  transform: scale(1.05);
}

.nav-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
  padding: 8px;
  background: #ecf5ff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-icon {
  background: #409EFF;
  color: #fff;
  transform: rotate(10deg);
}

.nav-name {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

.nav-item:hover .nav-name {
  color: #409EFF;
}

:deep(.el-card__body) {
  padding: 0;
}
</style>