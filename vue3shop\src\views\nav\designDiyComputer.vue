<template>
  <div class="diy-computer-page">
    <div class="page-header">
      <h1>DIY电脑配置</h1>
      <p>选择您心仪的电脑配件，打造专属配置</p>
    </div>

    <!-- 调试信息面板 -->
    <el-card v-if="categoryList.length > 0" class="debug-panel" style="margin-bottom: 20px; max-width: 800px; margin-left: auto; margin-right: auto;">
      <template #header>
        <span>数据库分类信息 (调试用)</span>
      </template>
      <div class="category-debug">
        <p><strong>当前数据库中的分类：</strong></p>
        <el-tag
          v-for="category in categoryList"
          :key="category.categoryId"
          style="margin: 2px;"
          type="info"
        >
          {{ category.categoryName }} (ID: {{ category.categoryId }})
        </el-tag>
      </div>
    </el-card>

    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="diyForm"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
      >
        <!-- 配置名称 -->
        <el-form-item label="配置名称" prop="configName">
          <el-input
            v-model="diyForm.configName"
            placeholder="请输入配置名称"
            clearable
          />
        </el-form-item>

        <!-- CPU -->
        <el-form-item label="CPU" prop="cpu">
          <el-select
            v-model="diyForm.cpu"
            placeholder="请选择CPU"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('CPU')"
          >
            <el-option
              v-if="cpuProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in cpuProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 显卡 -->
        <el-form-item label="显卡" prop="gpu">
          <el-select
            v-model="diyForm.gpu"
            placeholder="请选择显卡"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('显卡')"
          >
            <el-option
              v-if="gpuProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in gpuProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 主板 -->
        <el-form-item label="主板" prop="motherboard">
          <el-select
            v-model="diyForm.motherboard"
            placeholder="请选择主板"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('主板')"
          >
            <el-option
              v-if="motherboardProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in motherboardProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 内存 -->
        <el-form-item label="内存" prop="memory">
          <el-select
            v-model="diyForm.memory"
            placeholder="请选择内存"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('内存')"
          >
            <el-option
              v-if="memoryProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in memoryProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 存储 -->
        <el-form-item label="存储" prop="storage">
          <el-select
            v-model="diyForm.storage"
            placeholder="请选择存储设备"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('硬盘')"
          >
            <el-option
              v-if="storageProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in storageProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 电源 -->
        <el-form-item label="电源" prop="power">
          <el-select
            v-model="diyForm.power"
            placeholder="请选择电源"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('电源')"
          >
            <el-option
              v-if="powerProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in powerProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 机箱 -->
        <el-form-item label="机箱" prop="case">
          <el-select
            v-model="diyForm.case"
            placeholder="请选择机箱"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('机箱')"
          >
            <el-option
              v-if="caseProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in caseProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 散热器 -->
        <el-form-item label="散热器" prop="cooler">
          <el-select
            v-model="diyForm.cooler"
            placeholder="请选择散热器"
            clearable
            filterable
            style="width: 100%"
            @focus="loadProductsByCategory('散热')"
          >
            <el-option
              v-if="coolerProducts.length === 0"
              :value="null"
              disabled
              label="仓库暂时无货"
            />
            <el-option
              v-for="product in coolerProducts"
              :key="product.productId"
              :label="`${product.productName} - ¥${product.price}`"
              :value="product.productId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ product.productName }}</span>
                <span style="color: #f56c6c; font-weight: bold;">¥{{ product.price }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 配置总价 -->
        <el-form-item label="配置总价">
          <div class="total-price">
            <span class="price-label">当前配置总价：</span>
            <span class="price-value">¥{{ totalPrice.toFixed(2) }}</span>
          </div>
        </el-form-item>

        <!-- 预算 -->
        <el-form-item label="预算范围" prop="budget">
          <el-input-number
            v-model="diyForm.budget"
            :min="1000"
            :max="100000"
            :step="500"
            placeholder="请输入预算"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input
            v-model="diyForm.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入其他需求或备注"
          />
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            提交配置
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { productCategoryListService, productListByCategoryService } from '@/api/product'
import { saveDiyConfigService } from '@/api/diyConfig'
import { useUserStore } from '@/stores/user'

// 用户store
const userStore = useUserStore()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 分类列表
const categoryList = ref([])

// 各分类商品列表
const cpuProducts = ref([])
const gpuProducts = ref([])
const motherboardProducts = ref([])
const memoryProducts = ref([])
const storageProducts = ref([])
const powerProducts = ref([])
const caseProducts = ref([])
const coolerProducts = ref([])

// 表单数据
const diyForm = reactive({
  configName: '',
  cpu: '', // 存储CPU商品ID
  gpu: '', // 存储显卡商品ID
  motherboard: '',
  memory: '',
  storage: '',
  power: '',
  case: '',
  cooler: '',
  budget: null,
  remarks: ''
})

// 表单验证规则
const rules = {
  configName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  cpu: [
    { required: true, message: '请选择CPU', trigger: 'change' }
  ],
  gpu: [
    { required: true, message: '请选择显卡', trigger: 'change' }
  ],
  motherboard: [
    { required: true, message: '请选择主板', trigger: 'change' }
  ],
  memory: [
    { required: true, message: '请选择内存', trigger: 'change' }
  ],
  storage: [
    { required: true, message: '请选择存储设备', trigger: 'change' }
  ],
  power: [
    { required: true, message: '请选择电源', trigger: 'change' }
  ],
  case: [
    { required: true, message: '请选择机箱', trigger: 'change' }
  ],
  budget: [
    { required: true, message: '请输入预算', trigger: 'blur' },
    { type: 'number', min: 1000, message: '预算不能少于1000元', trigger: 'blur' }
  ]
}

// 获取分类列表
const getCategoryList = async () => {
  if (!userStore.checkLogin()) return

  try {
    loading.value = true
    console.log('开始获取分类列表...')
    const result = await productCategoryListService()
    console.log('分类列表API响应:', result)

    if (result && result.data) {
      categoryList.value = result.data
      console.log('获取分类列表成功，共', result.data.length, '个分类:')
      result.data.forEach(cat => {
        console.log(`- ${cat.categoryName} (ID: ${cat.categoryId})`)
      })
    } else {
      console.error('获取分类列表失败: 返回数据为空')
      ElMessage.error('获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error(`获取分类列表失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 根据分类名称获取商品列表
const loadProductsByCategory = async (categoryName) => {
  console.log(`开始加载分类商品: ${categoryName}`)
  console.log('当前分类列表:', categoryList.value)

  // 查找对应的分类ID
  const category = categoryList.value.find(cat => cat.categoryName === categoryName)
  if (!category) {
    console.log(`未找到分类: ${categoryName}`)
    console.log('可用的分类名称:', categoryList.value.map(cat => cat.categoryName))
    ElMessage.warning(`未找到分类: ${categoryName}`)
    return
  }

  console.log(`找到分类: ${categoryName}, ID: ${category.categoryId}`)

  try {
    const result = await productListByCategoryService(category.categoryId)
    console.log(`分类 ${categoryName} 的API响应:`, result)

    if (result && result.data) {
      // 根据分类名称将商品分配到对应的数组
      switch (categoryName) {
        case 'CPU':
          cpuProducts.value = result.data
          break
        case '显卡':
          gpuProducts.value = result.data
          break
        case '主板':
          motherboardProducts.value = result.data
          break
        case '内存':
          memoryProducts.value = result.data
          break
        case '硬盘':
        case '存储':
          storageProducts.value = result.data
          break
        case '电源':
          powerProducts.value = result.data
          break
        case '机箱':
          caseProducts.value = result.data
          break
        case '散热':
        case '散热器':
        case '风扇':
          coolerProducts.value = result.data
          break
        default:
          console.log(`未处理的分类: ${categoryName}`)
      }

      if (result.data.length === 0) {
        console.log(`分类 ${categoryName} 暂无商品`)
        ElMessage.info(`${categoryName} 分类暂无商品`)
      } else {
        console.log(`获取${categoryName}商品成功，共${result.data.length}个商品:`, result.data)
      }
    } else {
      console.log(`获取${categoryName}商品失败，返回数据为空`)
      ElMessage.error(`获取${categoryName}商品失败`)
    }
  } catch (error) {
    console.error(`获取${categoryName}商品失败:`, error)
    ElMessage.error(`获取${categoryName}商品失败: ${error.message}`)
  }
}

// 计算总价
const totalPrice = computed(() => {
  let total = 0

  // 获取选中商品的价格
  const getProductPrice = (productId, productList) => {
    const product = productList.find(p => p.productId === productId)
    return product ? parseFloat(product.price) : 0
  }

  // 累加各个配件的价格
  if (diyForm.cpu) {
    total += getProductPrice(diyForm.cpu, cpuProducts.value)
  }
  if (diyForm.gpu) {
    total += getProductPrice(diyForm.gpu, gpuProducts.value)
  }
  if (diyForm.motherboard) {
    total += getProductPrice(diyForm.motherboard, motherboardProducts.value)
  }
  if (diyForm.memory) {
    total += getProductPrice(diyForm.memory, memoryProducts.value)
  }
  if (diyForm.storage) {
    total += getProductPrice(diyForm.storage, storageProducts.value)
  }
  if (diyForm.power) {
    total += getProductPrice(diyForm.power, powerProducts.value)
  }
  if (diyForm.case) {
    total += getProductPrice(diyForm.case, caseProducts.value)
  }
  if (diyForm.cooler) {
    total += getProductPrice(diyForm.cooler, coolerProducts.value)
  }

  return total
})

// 提交表单
const submitForm = async () => {
  if (!userStore.checkLogin()) return

  try {
    // 表单验证
    await formRef.value.validate()

    // 确认提交
    await ElMessageBox.confirm(
      '确认提交这个DIY电脑配置吗？',
      '确认提交',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    submitting.value = true

    // 构造提交数据
    const submitData = {
      ...diyForm,
      userId: userStore.userInfo?.userId,
      createTime: new Date().toISOString()
    }

    console.log('提交的配置数据:', submitData)

    // 调用API保存配置
    await saveDiyConfigService(submitData)

    ElMessage.success('DIY配置提交成功！')

    // 重置表单
    resetForm()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(diyForm, {
    configName: '',
    cpu: '', // 重置为空字符串（商品ID）
    gpu: '', // 重置为空字符串（商品ID）
    motherboard: '',
    memory: '',
    storage: '',
    power: '',
    case: '',
    cooler: '',
    budget: null,
    remarks: ''
  })
}

// 页面加载时获取分类列表
onMounted(() => {
  getCategoryList()
})
</script>
<style lang="scss" scoped>
.diy-computer-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;

  .page-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;

    h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 1.1rem;
      opacity: 0.9;
      margin: 0;
    }
  }

  .form-card {
    max-width: 800px;
    margin: 0 auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: none;
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 40px;
    }

    .el-form {
      .el-form-item {
        margin-bottom: 25px;

        .el-form-item__label {
          font-weight: 600;
          color: #333;
          font-size: 14px;
        }

        .el-input,
        .el-select,
        .el-input-number {
          .el-input__inner,
          .el-input__wrapper {
            border-radius: 8px;
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
            }

            &:focus,
            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-textarea {
          .el-textarea__inner {
            border-radius: 8px;
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;
            resize: vertical;

            &:hover {
              border-color: #c0c4cc;
            }

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-button {
          border-radius: 8px;
          padding: 12px 30px;
          font-weight: 600;
          transition: all 0.3s ease;

          &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            }

            &:active {
              transform: translateY(0);
            }
          }

          &.el-button--default {
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      .el-form-item:last-child {
        margin-bottom: 0;
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #f0f0f0;

        .el-button {
          margin: 0 10px;
          min-width: 120px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .diy-computer-page {
    padding: 10px;

    .page-header {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .form-card {
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }
}

// 加载动画
.el-loading-mask {
  border-radius: 15px;
}

// 总价显示样式
.total-price {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  color: white;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

  .price-label {
    margin-right: 10px;
    font-weight: 500;
  }

  .price-value {
    font-size: 20px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

// 调试面板样式
.debug-panel {
  border: 2px dashed #e4e7ed;
  background: rgba(255, 255, 255, 0.9);

  .category-debug {
    p {
      margin-bottom: 10px;
      color: #606266;
      font-size: 14px;
    }
  }
}
</style>